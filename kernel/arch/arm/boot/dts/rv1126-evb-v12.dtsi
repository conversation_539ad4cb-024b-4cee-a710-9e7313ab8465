// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Rockchip Electronics Co., Ltd.
 */

#include "rv1126-evb-v10.dtsi"
/ {
	/delete-node/ vdd-npu;
	/delete-node/ vdd-vepu;

	vdd_logic: vdd-logic {
		compatible = "regulator-fixed";
		regulator-name = "vdd_logic";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <810000>;
		regulator-max-microvolt = <810000>;
	};

	camera_power{
	compatible = "vispect,mrv220_camera_power";
	status = "okay";
	power-gpios = <&gpio3 RK_PB0 GPIO_ACTIVE_HIGH>;
	};
};

&rk809 {
	regulators {
		/delete-node/ DCDC_REG1;
		vdd_npu_vepu: DCDC_REG1 {
			regulator-always-on;
			regulator-boot-on;
			regulator-min-microvolt = <650000>;
			regulator-max-microvolt = <950000>;
			regulator-ramp-delay = <6001>;
			regulator-initial-mode = <0x2>;
			regulator-name = "vdd_npu_vepu";
			regulator-state-mem {
				regulator-off-in-suspend;
			};
		};
	};
};

&npu {
	npu-supply = <&vdd_npu_vepu>;
};

&pwm0 {
	status = "disabled";
};

&pwm1 {
	status = "disabled";
};

&rkvenc {
	venc-supply = <&vdd_npu_vepu>;
};

&rkvenc_opp_table {
	/*
	 * max IR-drop values on different freq condition for this board!
	 */
	rockchip,board-irdrop = <
	     /* MHz	MHz	uV */
		500	594	50000
	>;
};
